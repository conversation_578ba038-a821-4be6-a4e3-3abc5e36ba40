import { defineComponent, ref, watch, shallowReactive } from 'vue'
import * as echarts from 'echarts'

// 声明类型
const PropsType = {
  cdata: {
    type: Object,
    require: true,
  },
} as const

// 定义主体
export default defineComponent({
  props: PropsType,
  setup(props) {
    // 定义 ref
    const chartRef = ref()
    // 配置项
    let options = {}

    watch(
      () => props.cdata,
      (val: any) => {
        options = {
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'transparent',
            axisPointer: {
              lineStyle: {
                color: '#3763cd', // 显示竖线颜色
                type: 'solid',
              },
            },
            textStyle: {
              color: '#ffffff',
            },
          },
          grid: {
            left: '3%',
            right: '5%',
            bottom: '5%',
            top: '25%',
            containLabel: true,
          },
          legend: {
            //设置icon宽高
            itemWidth: 12,
            itemHeight: 12,
            //设置icon位置
            right: 5,
            top: 6,
            //设置icon 温度 湿度间距
            itemGap: 30,
            data: [
              {
                name: '入库单',
              },
              {
                name: '出库单',
              },
            ],
            textStyle: {
              color: '#a8aab0',
              fontStyle: 'normal',
              fontSize: 14,
            },
          },
          xAxis: [
            {
              type: 'category',
              //	boundaryGap: true,//坐标轴两边留白
              data: val.xData ,
              axisLabel: {
                //坐标轴刻度标签的相关设置。
                //		interval: 0,//设置为 1，表示『隔一个标签显示一个标签』
                //	margin:15,
                textStyle: {
                  color: '#a8aab0',
                  fontStyle: 'normal',
                  fontSize: 12,
                },
                rotate: 0,
              },
              axisTick: {
                //坐标轴刻度相关设置。
                show: false,
              },
              axisLine: {
                //坐标轴轴线相关设置
                lineStyle: {
                  color: '#2B66D7',
                  opacity: 0.8,
                },
              },
              splitLine: {
                //坐标轴在 grid 区域中的分隔线。
                show: false,
              },
            },
          ],
          yAxis: [
            {
              type: 'value',
              splitNumber: 5,
              axisLabel: {
                textStyle: {
                  color: '#a8aab0',
                  fontStyle: 'normal',
                  fontSize: 12,
                },
              },
              axisLine: {
                show: false,
              },
              axisTick: {
                show: false,
              },
              splitLine: {
                show: true,
                lineStyle: {
                  type: 'dashed',
                  width: 1,
                  color: 'rgba(255, 255, 255, 0.2)',
                },
              },
            },
          ],
          series: [
            {
              name: '出库单',
              type: 'bar',
              data: val.outCount,
              barWidth: 8,

              itemStyle: {
                normal: {
                  show: true,
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#2B66D7',
                    },
                    {
                      offset: 1,
                      color: '#2B66D7',
                    },
                  ]),
                  barBorderRadius: 0,
                  borderWidth: 0,
                },
              },
            },
            {
              name: '入库单',
              type: 'bar',
              data: val.onCount,
              barWidth: 8,
              itemStyle: {
                normal: {
                  show: true,
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#05B8CD',
                    },
                    {
                      offset: 1,
                      color: '#05B8CD',
                    },
                  ]),
                  barBorderRadius: 0,
                  borderWidth: 0,
                },
              },
            },
          ],
        }

        // 手动触发更新
        if (chartRef.value) {
          // 通过初始化参数打入数据
          chartRef.value.initChart(options)
        }
      },
      {
        immediate: true,
        deep: true,
      },
    )

    return () => {
      const height = '300px'
      const width = '480px'

      return (
        <div>
          <echart ref={chartRef} options={options} height={height} width={width} />
        </div>
      )
    }
  },
})
