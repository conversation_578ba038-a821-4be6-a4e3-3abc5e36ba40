<template>
  <div id="indexs" ref="appRef">
    <div class="bg">
      <!-- title -->
      <div class="titleText">{{ title }}</div>
      <!-- logo -->
      <div style="position: absolute; top: 50px; left: 44px">
        <img src="@/assets/logoImg.png" alt="" style="width: 174px; height: 40px" />
      </div>
      <!-- time -->
      <div class="timeText">
        <div class="timet">
          {{ dateData.dateDay }}
        </div>
        <div class="timeb">
          <span>{{ dateData.dateYear }}</span
          ><span style="margin-left: 4px">{{ dateData.dateWeek }}</span>
        </div>
      </div>
      <!-- left  -->
      <div class="leftImg">
        <div>
          <img src="@/assets/home.png" style="cursor: pointer" @click="homepage" />
        </div>
        <div v-for="(img, index) in images" :key="index" @click="selectImage(index)">
          <div style="display: flex">
            <img :src="img.selected ? img.selectSrc : img.src" style="cursor: pointer" />
            <div class="rightBack" style="margin-left: 6px">{{ img.name }}</div>
          </div>
        </div>
      </div>

      <!-- 监控 -->
      <div class="monitor">
        <!-- 单屏 -->
        <div class="monitorOne" v-if="target === 0"></div>

        <!-- 四分屏 -->
        <div class="monitorFour" v-if="target === 1">
          <div v-for="item in singleScreenMonitoring" >
            <div class="iframgesd">
              <iframe width="780px" height="444px" :src="item.videoPath" frameborder="0"></iframe>
            </div>
          </div>
        </div>

        <!-- 九分屏 -->
        <div class="monitorNine" v-if="target === 2">
          <div v-for="item in singleScreenMonitoring" >
            <div class="iframgesdnine">
              <iframe width="516px" height="292px" :src="item.videoPath" frameborder="0"></iframe>
            </div>
          </div>
        </div>

        <!-- 十六分屏 -->
        <div class="monitorSixteen" v-if="target === 3">

          <div v-for="item in singleScreenMonitoring" >
            <div class="iframgesdsix">
              <iframe width="384px" height="216px" :src="item.videoPath" frameborder="0"></iframe>
            </div>
          </div>
        </div>

        <!-- 左右切换 -->
        <img
          src="@/assets/onLefts.png"
          alt=""
          @click="previousPageClick"
          style="
            width: 28px;
            height: 42px;
            position: absolute;
            top: 563px;
            left: 100px;
            cursor: pointer
          " />
        <img
          src="@/assets/onRight.png"
          alt=""
          @click="nextPageClick"

          style="
            width: 28px;
            height: 42px;
            position: absolute;
            top: 563px;
            right: 100px;
            cursor: pointer

          " />
      </div>
    </div>
  </div>
</template>

<script setup>
import useDraw from '@/utils/useDraw'
import { ref, reactive, onMounted } from 'vue'
import { formatTime } from '@/utils/index'
import { useRouter } from 'vue-router'
const { appRef, calcRate, windowDraw, unWindowDraw } = useDraw()
//title
let title = ref('智慧戎仓大数据智控平台')
//leftTop_logo

//rightTop_time
const dateData = reactive({
  dateDay: '',
  dateYear: [],
  dateWeek: '',
  timing: null,
})

const weekday = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
const timeFn = () => {
  dateData.timing = setInterval(() => {
    const time = formatTime(new Date(), 'HH:mm:ss')
    dateData.dateDay = time
    const month =
      new Date().getMonth() + 1 < 10
        ? '0' + (new Date().getMonth() + 1)
        : new Date().getMonth() + 1
    const day =
      new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
    const year = new Date().getFullYear() + '.' + month + '.' + day
    dateData.dateYear = year
    //获取星期几
    const week = weekday[new Date().getDay()]
    dateData.dateWeek = week
  }, 16)
}



//图标
let images = ref([
  {
    src: require('@/assets/oneImgs.png'),
    name: '单屏',
    selectSrc: require('@/assets/oneSelect.png'),
    selected: false,
  },
  {
    src: require('@/assets/fourNo.png'),
    name: '四分屏',
    selectSrc: require('@/assets/fourSelect.png'),
    selected: false,
  },
  {
    src: require('@/assets/nineNo.png'),
    name: '九分屏',
    selectSrc: require('@/assets/nineSelect.png'),
    selected: false,
  },
  {
    src: require('@/assets/sixNo.png'),
    name: '十六分屏',
    selectSrc: require('@/assets/sixSelect.png'),
    selected: false,
  },
])
let target = ref(3)
const selectImage = (index) => {
  target.value = index
  images.value.forEach((image, i) => {
    image.selected = i === index
  })
}

//首页
const router = useRouter()
const homepage = () => {
  router.push('/')
}
onMounted(() => {
  timeFn()
  selectImage(target.value)
  // todo 屏幕适应
  windowDraw()
  calcRate()
})







//单屏
let singleScreenMonitoring = ref([
  {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  },
  {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  },
  {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  },
  {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  },
  {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }, {
    videoPath:
      'https://www.bilibili.com/video/BV11hugz5EGo/?spm_id_from=333.1007.tianma.1-1-1.click&vd_source=a2bf04277fb6ffcdb9a50d1783381cc7',
  }
])
</script>

<style lang="scss" scoped>
#indexs {
  color: #d3d6dd;
  width: 1920px;
  height: 1080px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transform-origin: left top;
  .bg {
    width: 100%;
    height: 100%;
    // padding: 16px 16px 0 16px;
    background-image: url('../../assets/jkbacks.png');
    background-size: cover;
    background-position: center center;
    box-sizing: border-box;
  }
}

// title
.titleText {
  position: absolute;
  top: 18px;
  left: 50%;
  transform: translateX(-50%);
  font-family: 'alim';
  font-size: 44px;
  font-style: normal;
  line-height: 60px;
  background: linear-gradient(180deg, #a2ffda 0%, #fff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
//time
.timeText {
  position: absolute;
  top: 40px;
  right: 50px;
  color: #c0dcde;

  width: 130px;
  height: 48px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  .timet {
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
  }
  .timeb {
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }
}

.monitor {
  .monitorOne {
    position: absolute;
    top: 135px;
    left: 174px;
    width: 1572px;
    height: 900px;
    background-image: url('../../assets/oneBack.png');
    background-size: 1572px 900px;
    background-repeat: no-repeat;
  }

}
.leftImg {
  position: absolute;
  top: 135px;
  left: 50px;
  width: 228px;
  height: 220px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  img {
    width: 28px;
    height: 28px;
  }
}

.rightBack {
  width: 78px;
  height: 26px;
  background-image: url('../../assets/dp.png');
  background-size: 78px 26px;
  background-repeat: no-repeat;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: #eff7ff;
  font-family: 'alia';
  font-size: 14px;
  font-style: normal;
}

.leftImg img:hover + .rightBack {
  opacity: 1;
}


.monitorFour {
    position: absolute;
    top: 135px;
    left: 174px;
    width: 1572px;
    height: 900px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  align-content: space-between;

  }

.iframgesd{
  width: 780px;
  height: 444px;
  background-image: url('../../assets/fourBacks.png');
  background-size: 780px 444px;
  background-repeat: no-repeat;
}

.monitorNine{
  position: absolute;
    top: 135px;
    left: 174px;
    width: 1572px;
    height: 900px;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
  align-content: flex-start;
  gap:12px 12px;
}
.iframgesdnine{
  width: 516px;
  height: 292px;
  background-image: url('../../assets/fourBacks.png');
  background-size: 516px 292px;
  background-repeat: no-repeat;
}

.monitorSixteen{
  position: absolute;
    top: 135px;
    left: 174px;
    width: 1572px;
    height: 900px;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
  align-content: flex-start;
  gap:12px 12px;

}
.iframgesdsix{
  width:384px;
  height: 216px;
  background-image: url('../../assets/sixsback.png');
  background-size: 384px 216px;
  background-repeat: no-repeat;
}
</style>
