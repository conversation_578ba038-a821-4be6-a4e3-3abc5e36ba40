import { defineComponent, reactive, watch } from 'vue'
import Draw from './draw'

export default defineComponent({
  props: {
    basicData: {
      type: Object,
      default: () => ({
        months: [],
        onStorehouseStatisticsList: [],
        outStorehouseStatisticsList: [],
      }),
    },
  },
  setup(props) {
    const cdata = reactive({
      xData: [],
      onCount: [],
      outCount: [],
    })

    watch(
      () => props.basicData,
      (newVal) => {
        cdata.xData = newVal.months || []
        cdata.onCount =
          newVal.onStorehouseStatisticsList?.map((item) => item.onCount) || []
        cdata.outCount =
          newVal.outStorehouseStatisticsList?.map((item) => item.outCount) || []
      },
      { immediate: true, deep: true },
    )

    return () => (
      <div>
        <Draw cdata={cdata} />
      </div>
    )
  },
})
