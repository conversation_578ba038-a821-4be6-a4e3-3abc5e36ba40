import request from '../utils/request'


//仓库信息统计
export function warehouseInformation() {
  return request({
    url: '/admin/largeScreen/getWarehouseInfoStatistics',
    method: 'get',
  })
}
//出库频率排行
export function outboundFrequencyRanking() {
  return request({
    url: '/admin/largeScreen/getOutboundFrequency',
    method: 'get',
  })
}
//出入库统计
export function inboundOutboundStatistics() {
  return request({
    url: '/admin/largeScreen/getOutOnStorehouseStatistics',
    method: 'get',
  })
}


//物资统计  center
export function materialStatistics() {
  return request({
    url: '/admin/largeScreen/getMaterialStatistic',
    method: 'get',
  })
}
//库存预警率
export function inventoryWarningRate() {
  return request({
    url: '/admin/largeScreen/getMaterialInventory',
    method: 'get',
  })
}
//过期预警数量
export function expirationWarningQuantity() {
  return request({
    url: '/admin/largeScreen/getMaterialCatalogExpired',
    method: 'get',
  })
}


//租户信息
export function tenant() {
  return request({
    url: '/admin/tenant/list',
    method: 'get',
  })
}