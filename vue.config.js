const { defineConfig } = require('@vue/cli-service')
const webpack = require('webpack')

module.exports = defineConfig({
  transpileDependencies: true,
  lintOnSave: false,
  configureWebpack: {
    plugins: [
      new webpack.DefinePlugin({
        __VUE_I18N_LEGACY_API__: JSON.stringify(false),
        __VUE_I18N_FULL_INSTALL__: JSON.stringify(false),
        __INTLIFY_PROD_DEVTOOLS__: JSON.stringify(false),
        __VERSION__: JSON.stringify(process.env.npm_package_version),
        __NEXT_NAME__: JSON.stringify(process.env.npm_package_name),
      }),
    ],
  },
  // 配置开发服务器代理
  devServer: {
    proxy: {
      '/api': {
        target:  process.env.VITE_ADMIN_PROXY_PATH, // 目标服务器地址,
        changeOrigin: true,
        pathRewrite: {
          '^/api': '',
        },
        // 支持 websocket
        ws: true,
        // 不验证 SSL 证书（若后端使用 HTTPS 且证书有问题时使用）
        secure: false,
      },
    },
  },
})
