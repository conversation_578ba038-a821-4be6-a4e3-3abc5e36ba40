<template>
  <div class="centerRight2">
    <div>
      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="displayData"
        :bordered="false"
        :bottom-bordered="false"
        :virtual-scroll="true"
        size="small"
        :single-line="false"
        :max-height="214"
        :row-class-name="getRowClassName" />
    </div>
  </div>
</template>

<script lang="ts">
import { inventoryWarningRate } from '../../api/index'

import { defineComponent, ref, h, onMounted, onUnmounted, computed } from 'vue'
import { NDataTable } from 'naive-ui'
// @ts-ignore
import numOne from '@/assets/numOne.png'
// @ts-ignore
import numx from '@/assets/numx.png'
// @ts-ignore
import numTwo from '@/assets/numTwo.png'
// @ts-ignore
import numThree from '@/assets/numThree.png'

function createColumns() {
  return [
    {
      title: '序号',
      key: 'key',
      align: 'center',
      width: 100,
      render(row: any) {
        if (row.key === 1) {
          return h('img', {
            src: numOne,
            style: {
              width: '24px',
              height: '26px',
              display: 'block',
              margin: '0 auto',
            },
          })
        } else if (row.key === 2) {
          return h('img', {
            src: numTwo,
            style: {
              width: '24px',
              height: '26px',
              display: 'block',
              margin: '0 auto',
            },
          })
        } else if (row.key === 3) {
          return h('img', {
            src: numThree,
            style: {
              width: '24px',
              height: '26px',
              display: 'block',
              margin: '0 auto',
            },
          })
        } else {
          return h(
            'div',
            {
              style: {
                backgroundImage: `url(${numx})`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center center',
                width: '80px',
                height: '26px',

                color: '#fff',
              },
            },
            row.key.toString(),
          )
        }
      },
    },
    {
      title: '仓库',
      key: 'warehouseName',
      align: 'center',
    },
    {
      title: '物资名称',
      key: ' materialName',
      width: 400,
      ellipsis: true,
    },
    {
      title: '库存/预警值',
      key: 'num',
      align: 'center',
    },
  ]
}

export default defineComponent({
  components: {
    NDataTable,
  },
  setup() {
    const tableRef = ref()
    const scrollTimer = ref<number | null>(null)
    const currentIndex = ref(0)
    const itemsPerPage = 5
    const scrollInterval = 3000

    const data: any = ref([])

    const displayData = computed(() => {
      if (data.value.length <= itemsPerPage) {
        return data.value
      }

      const result = []
      for (let i = 0; i < itemsPerPage; i++) {
        const index = (currentIndex.value + i) % data.value.length
        result.push(data.value[index])
      }
      return result
    })

    // 开始自动滚动
    const startAutoScroll = () => {
      if (data.value.length <= itemsPerPage) return

      scrollTimer.value = setInterval(() => {
        currentIndex.value = (currentIndex.value + 1) % data.value.length
      }, scrollInterval)
    }

    // 停止自动滚动
    const stopAutoScroll = () => {
      if (scrollTimer.value) {
        clearInterval(scrollTimer.value)
        scrollTimer.value = null
      }
    }

    const getData = async () => {
      try {
        const res = await inventoryWarningRate()
        data.value = res?.data || []
      } catch (error) {}
    }
    let intervalId = null
    const requestTime = () => {
      const now = new Date()
      const nextHour = new Date(now)
      nextHour.setHours(now.getHours() + 1, 0, 0, 0)
      const delay = nextHour.getTime() - Date.now()

      setTimeout(() => {
        getData()
        intervalId = setInterval(getData, 60 * 60 * 1000)
      }, delay)
    }

    const stopRequesting = () => {
      if (intervalId) {
        clearInterval(intervalId)
        intervalId = null
      }
    }

    onMounted(() => {
      getData()
      requestTime()
      startAutoScroll()
    })

    onUnmounted(() => {
      stopRequesting()

      stopAutoScroll()
    })

    // 根据数据的key值设置行类名
    const getRowClassName = (rowData: any) => {
      return rowData.key % 2 === 1 ? 'odd-row' : 'even-row'
    }

    return {
      tableRef,
      displayData,
      columns: createColumns(),
      getRowClassName,
    }
  },
})
</script>

<style lang="scss" scoped>
$box-height: 257px;
$box-width: 812px;
.centerRight2 {
  height: $box-height;
  width: $box-width;
}

:deep(.n-data-table .n-data-table-th) {
  color: #effff9;
  text-align: center;
  font-family: 'alia';
  font-size: 14px;
  border: none;
  background-color: #0b3a31;
}

:deep(.n-data-table .n-data-table-td) {
  border: none;
  background-color: #0b3a31;
  color: #effff9;
  font-family: 'alia';
  font-size: 14px;
}

:deep(.n-data-table .n-data-table-table) {
  width: 100%;
  word-break: break-word;
  border-collapse: separate; /* 使用separate来创建行间隙 */
  border-spacing: 0 4px; /* 列间隙为0，行间隙为4px，减小间距 */
  background-color: transparent; /* 表格背景透明 */
  margin-bottom: -4px !important;
  color: #effff9 !important;
}

/* 扩大行间隙 */
:deep(.n-data-table .n-data-table-tr) {
  background-color: transparent; /* 行背景透明 */
  height: 36px; /* 设置行高为36px */
}

:deep(.n-data-table .n-data-table-td),
:deep(.n-data-table .n-data-table-th) {
  padding: 6px; /* 调整内边距 */
  height: 36px; /* 设置行高为36px */
  line-height: 20px; /* 设置行内文字行高 */
}

/* 确保表头和表体之间间距一致 */
:deep(.n-data-table .n-data-table-thead) {
  margin-bottom: 0;
}

:deep(.n-data-table .n-data-table-tbody) {
  margin-top: 0;
}

:deep(.n-data-table .n-data-table-tr:not(.n-data-table-tr--summary):hover) {
  background-color: #0b3a31 !important;
}

:deep(.n-data-table
    .n-data-table-tr:not(.n-data-table-tr--summary):hover
    .n-data-table-td) {
  background-color: #0b3a31 !important;
}

/* 奇数行背景色设置 */
:deep(.n-data-table .n-data-table-tbody .n-data-table-tr.odd-row .n-data-table-td) {
  background-color: #093029 !important;
}

/* 偶数行背景色保持不变 */
:deep(.n-data-table .n-data-table-tbody .n-data-table-tr.even-row .n-data-table-td) {
  background-color: #0b3a31 !important;
}
</style>
