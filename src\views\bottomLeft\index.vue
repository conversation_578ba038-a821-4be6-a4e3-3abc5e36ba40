<template>
  <div class="d-flex jc-center">
    <chart :basic-data="progressData" />
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, onUnmounted,ref } from 'vue'
import Chart from './chart/index'


import {inboundOutboundStatistics} from '../../api/index'
const progressData = ref({})
const getData = async () => {
  try {
    const res = await inboundOutboundStatistics()
    progressData.value = res.data || {}

  } catch (error) {
  }
}
let intervalId:any= null;
const requestTime = () => {
  const now = new Date();
  const nextHour = new Date(now);
  nextHour.setHours(now.getHours() + 1, 0, 0, 0);
  const delay = nextHour.getTime() - Date.now();

  setTimeout(() => {
    getData();
    intervalId = setInterval(getData, 60*60*1000);
  }, delay);
};

const stopRequesting = () => {
  if (intervalId) {
    clearInterval(intervalId);
    intervalId = null;
  }
};
onUnmounted(() => {
  stopRequesting()
})
onMounted(() => {
  getData()
  requestTime()
})




</script>

<style lang="scss" scoped></style>
