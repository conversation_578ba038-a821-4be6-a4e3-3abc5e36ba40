<template>
  <div class="center-left">
    <div class="scroll-container">
      <div v-for="(item, index) in displayData" :key="index" class="progress-item">
        <div class="top-text">
          <div class="progress-label">
            <div style="width: 42px; height: 22px; margin-right: 10px; text-align: left">
              NO.{{ item.originalIndex }}
            </div>
            <div>{{ item.materialName }}</div>
          </div>
          <div :class="item.originalIndex == 1 ? 'progress-num' : 'progress-nums'">
           {{ item.expiredNum }}
          </div>
        </div>
        <div class="progress-container">
          <div
            class="progress-bar"
            :style="getProgressStyle(item.expiredNum, item.originalIndex)"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

import {expirationWarningQuantity} from '../../api/index'
const progressData = ref([

])
//最大值
let maxNum=ref(0)

const getData = async () => {
  try {
    const res = await expirationWarningQuantity()
    progressData.value = res.data || []
    maxNum.value = Math.max(...progressData.value.map(item => item.expiredNum))

  } catch (error) {
  }
}

// 当前显示的起始索引
const currentIndex = ref(0)
const maxDisplayItems = 5

// 计算显示的数据（最多5条，带原始索引）
const displayData = computed(() => {
  const startIndex = currentIndex.value
  const endIndex = startIndex + maxDisplayItems

  return progressData.value.slice(startIndex, endIndex).map((item, index) => ({
    ...item,
    originalIndex: startIndex + index + 1,
  }))
})

// 自动滚动定时器
let scrollTimer = null

// 开始自动滚动
const startAutoScroll = () => {
  scrollTimer = setInterval(() => {
    if (progressData.value.length > maxDisplayItems) {
      currentIndex.value =
        (currentIndex.value + 1) % (progressData.value.length - maxDisplayItems + 1)
    }
  }, 3000) // 每3秒滚动一次
}

// 停止自动滚动
const stopAutoScroll = () => {
  if (scrollTimer) {
    clearInterval(scrollTimer)
    scrollTimer = null
  }
}







let intervalId= null;
const requestTime = () => {
  const now = new Date();
  const nextHour = new Date(now);
  nextHour.setHours(now.getHours() + 1, 0, 0, 0);
  const delay = nextHour.getTime() - Date.now();

  setTimeout(() => {
    getData();
    intervalId = setInterval(getData, 60*60*1000);
  }, delay);
};

const stopRequesting = () => {
  if (intervalId) {
    clearInterval(intervalId);
    intervalId = null;
  }
};

onMounted(async() => {
  await getData()
  requestTime()
  startAutoScroll()
})

onUnmounted(() => {
  stopRequesting()
  stopAutoScroll()
})



// 渐变色配置
const gradientColors = [
  'linear-gradient(90deg, #58390A 0%, #F4BF70 73.67%, #FEF6DF 100%)',
  'linear-gradient(90deg, #0F4B38 0%, #22B081 73.67%, #DDFCF2 100%)',
  'linear-gradient(90deg, #45b7d1, #74c7ec)',
  'linear-gradient(90deg, #96ceb4, #b8e6d1)',
]

// 获取进度条样式
const getProgressStyle = (value, index) => {
  return {
    width: `${value/maxNum.value*100}%`,
    background: index == 1 ? gradientColors[0] : gradientColors[1],
    height: '100%',
  }
}
</script>

<style scoped lang="scss">
.title {
  font-size: 18px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20px;
}

.progress-item {
  width: 456px;
  height: 37px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .top-text {
    display: flex;
    justify-content: space-between;
    height: 22px;
    align-items: center;
  }
}

.progress-label {
  display: flex;
  align-items: center;
  color: #dff5ec;
  text-align: center;
  font-family: 'alia';
  font-size: 14px;
  font-style: normal;
  font-weight: 55 Regular;
  line-height: normal;
}
.progress-num {
  text-align: center;
  font-family: 'alia';
  font-size: 16px;
  font-style: normal;
  font-weight: 65 Medium;
  line-height: normal;
  background: linear-gradient(180deg, #ffd699 0%, #fff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.progress-nums {
  text-align: center;
  font-family: 'alia';
  font-size: 16px;
  font-style: normal;
  font-weight: 65 Medium;
  line-height: normal;
  background: linear-gradient(180deg, #99ffd6 0%, #fff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.progress-container {
  position: relative;
  width: 456px;
  height: 10px;
  flex-shrink: 0;
  background: #0a3030;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  transition: width 0.3s ease-in-out;
}
.center-left {
  width: 456px;
  height: 235px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.scroll-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  overflow: hidden;
  position: relative;
}

.scroll-container .progress-item {
  flex-shrink: 0; /* 防止flex压缩高度 */
  transition: transform 0.5s ease-in-out;
}
</style>
